use crate::services::ServiceError;
use serde_json::json;

/// Central error handling function that formats output based on JSON flag
pub fn handle_error(err: &ServiceError, is_json: bool) -> ! {
    if is_json {
        println!(
            "{}",
            json!({
                "error": true,
                "message": err.to_string(),
                "type": match err {
                    ServiceError::Network { .. } => "NetworkError",
                    ServiceError::Validation { .. } => "ValidationError",
                    ServiceError::Config { .. } => "ConfigError",
                    ServiceError::Auth { .. } => "AuthError",
                    ServiceError::Metadata { .. } => "MetadataError",
                    ServiceError::Contract { .. } => "ContractError",
                    ServiceError::FileSystem { .. } => "FileSystemError",
                    ServiceError::Internal { .. } => "InternalError",
                }
            })
        );
    } else {
        eprintln!("Error: {}", err);
    }
    std::process::exit(1);
}

/// Check if a command requires JSON output
pub fn check_is_json(cmd: &crate::Command) -> bool {
    use crate::Command;
    match cmd {
        Command::Build(build) => build.is_json(),
        Command::Call(call) => call.is_json(),
        Command::Deploy(deploy) => deploy.is_json(),
        Command::Query(query) => query.is_json(),
        Command::Upgrade(upgrade) => upgrade.is_json(),
        Command::Fork(fork) => fork.is_json(),
        Command::Receipt(receipt) => receipt.is_json(),
        // KeyCommand and ConfigCommand don't have verbosity flags
        Command::Key(_) => false,
        Command::Config(_) => false,
        // Commands without verbosity flags default to false
        Command::New { .. } | Command::Version => false,
    }
}



#[cfg(test)]
mod tests {
    use super::*;
    use crate::services::ServiceError;

    #[test]
    fn test_format_output_error_json() {
        let result = format_output_error("Test error", true);
        assert!(result.contains("\"error\":true"));
        assert!(result.contains("\"message\":\"Test error\""));
        assert!(result.contains("\"success\":false"));
    }

    #[test]
    fn test_format_output_error_plain() {
        let result = format_output_error("Test error", false);
        assert_eq!(result, "Error: Test error");
    }

    #[test]
    fn test_convert_anyhow_err() {
        let anyhow_err = anyhow::anyhow!("Test anyhow error");
        let service_err = convert_anyhow_err(anyhow_err);
        
        match service_err {
            ServiceError::Internal { message } => {
                assert!(message.contains("Test anyhow error"));
            }
            _ => panic!("Expected Internal error"),
        }
    }

    #[test]
    fn test_with_context() {
        let original_err = ServiceError::validation("Original error");
        let result: Result<(), ServiceError> = Err(original_err);
        
        let contextual_result = with_context(result, "Additional context");
        
        match contextual_result {
            Err(ServiceError::Internal { message }) => {
                assert!(message.contains("Additional context"));
                assert!(message.contains("Original error"));
            }
            _ => panic!("Expected Internal error with context"),
        }
    }
}
