use crate::services::ServiceError;
use serde_json::json;

/// Central error handling function that formats output based on JSON flag
pub fn handle_error(err: &ServiceError, is_json: bool) -> ! {
    if is_json {
        println!(
            "{}",
            json!({
                "error": true,
                "message": err.to_string(),
                "type": match err {
                    ServiceError::Network { .. } => "NetworkError",
                    ServiceError::Validation { .. } => "ValidationError",
                    ServiceError::Config { .. } => "ConfigError",
                    ServiceError::Auth { .. } => "AuthError",
                    ServiceError::Metadata { .. } => "MetadataError",
                    ServiceError::Contract { .. } => "ContractError",
                    ServiceError::FileSystem { .. } => "FileSystemError",
                    ServiceError::Internal { .. } => "InternalError",
                }
            })
        );
    } else {
        eprintln!("Error: {}", err);
    }
    std::process::exit(1);
}

/// Check if a command requires JSON output
pub fn check_is_json(cmd: &crate::Command) -> bool {
    use crate::Command;
    match cmd {
        Command::Build(build) => {
            use contract_build::VerbosityFlags;
            // Check if verbosity flags indicate JSON output
            build.verbosity.is_json()
        }
        Command::Call(call) => call.verbosity.is_json(),
        Command::Deploy(deploy) => deploy.verbosity.is_json(),
        Command::Query(query) => query.verbosity.is_json(),
        Command::Upgrade(upgrade) => upgrade.verbosity.is_json(),
        Command::Fork(fork) => fork.verbosity.is_json(),
        Command::Receipt(receipt) => receipt.verbosity.is_json(),
        Command::Key(key) => key.verbosity.is_json(),
        Command::Config(config) => config.verbosity.is_json(),
        // Commands without verbosity flags default to false
        Command::New { .. } | Command::Version => false,
    }
}

/// Convert anyhow::Error to ServiceError
pub fn convert_anyhow_err(err: anyhow::Error) -> ServiceError {
    ServiceError::internal(err.to_string())
}

/// Format error output as JSON or plain text
pub fn format_output_error(message: &str, is_json: bool) -> String {
    if is_json {
        json!({
            "error": true,
            "message": message,
            "success": false
        })
        .to_string()
    } else {
        format!("Error: {}", message)
    }
}

/// Add context to a ServiceResult
pub fn with_context<T>(
    result: Result<T, ServiceError>,
    context: &str,
) -> Result<T, ServiceError> {
    result.map_err(|err| ServiceError::internal(format!("{}: {}", context, err)))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::services::ServiceError;

    #[test]
    fn test_format_output_error_json() {
        let result = format_output_error("Test error", true);
        assert!(result.contains("\"error\":true"));
        assert!(result.contains("\"message\":\"Test error\""));
        assert!(result.contains("\"success\":false"));
    }

    #[test]
    fn test_format_output_error_plain() {
        let result = format_output_error("Test error", false);
        assert_eq!(result, "Error: Test error");
    }

    #[test]
    fn test_convert_anyhow_err() {
        let anyhow_err = anyhow::anyhow!("Test anyhow error");
        let service_err = convert_anyhow_err(anyhow_err);
        
        match service_err {
            ServiceError::Internal { message } => {
                assert!(message.contains("Test anyhow error"));
            }
            _ => panic!("Expected Internal error"),
        }
    }

    #[test]
    fn test_with_context() {
        let original_err = ServiceError::validation("Original error");
        let result: Result<(), ServiceError> = Err(original_err);
        
        let contextual_result = with_context(result, "Additional context");
        
        match contextual_result {
            Err(ServiceError::Internal { message }) => {
                assert!(message.contains("Additional context"));
                assert!(message.contains("Original error"));
            }
            _ => panic!("Expected Internal error with context"),
        }
    }
}
