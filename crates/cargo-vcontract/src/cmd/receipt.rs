use anyhow::{anyhow, Result};
use crate::services::{ServiceError, ServiceResult};
use clap::Args;
use colored::Colorize;
use contract_build::VerbosityFlags;
use serde_json::Value;
use std::convert::TryFrom;
use tokio::runtime::Runtime;

use crate::cmd::config::get_node_url;
use crate::cmd::utils::{connect_node, format_error, format_output};

/// Query transaction receipt by hash
#[derive(Debug, Args)]
#[clap(name = "receipt")]
pub struct ReceiptCommand {
    /// Transaction hash to query
    #[clap(value_parser)]
    transaction_hash: String,

    /// Node URL (override config file setting - currently configured: use 'cargo vcontract config' to view/change)
    #[clap(long)]
    node: Option<String>,

    /// Verbosity flags
    #[clap(flatten)]
    verbosity: VerbosityFlags,
}

impl ReceiptCommand {
    pub fn exec(&self) -> ServiceResult<()> {
        // Create tokio runtime
        let runtime = Runtime::new()
            .map_err(|e| ServiceError::internal(format!("Failed to create tokio runtime: {}", e)))?;
        runtime.block_on(self.async_exec())
    }

    async fn async_exec(&self) -> ServiceResult<()> {
        let verbosity = match contract_build::Verbosity::try_from(&self.verbosity) {
            Ok(v) => v,
            Err(e) => {
                let error_msg = e.to_string();
                if self.verbosity.is_json() {
                    println!("{}", format_error(&error_msg, true));
                    return Ok(());
                } else {
                    return Err(e);
                }
            }
        };
        let verbose = verbosity.is_verbose();

        // Connect to node
        let node_url = get_node_url(self.node.as_deref().unwrap_or("127.0.0.1:9877"))?;
        let client = connect_node(&node_url, verbosity.is_json()).await?;

        // Query transaction receipt
        if verbose && !verbosity.is_json() {
            println!("Querying receipt for: {}", self.transaction_hash.yellow());
        }

        let receipt = match client.get_transaction_receipt(&self.transaction_hash).await {
            Ok(receipt) => receipt,
            Err(e) => {
                let error_msg = format!("Failed to get transaction receipt: {}", e);
                if verbosity.is_json() {
                    println!("{}", format_error(&error_msg, true));
                    return Ok(());
                } else {
                    return Err(anyhow!(error_msg));
                }
            }
        };

        // Check if receipt was found
        if receipt.is_null() {
            let error_msg = format!("Transaction not found: {}", self.transaction_hash);
            if verbosity.is_json() {
                println!("{}", format_error(&error_msg, true));
                return Ok(());
            } else {
                return Err(anyhow!(error_msg));
            }
        }

        // Parse receipt for basic information
        let status = receipt
            .get("receipt")
            .and_then(|r| r.get("status"))
            .and_then(Value::as_bool)
            .unwrap_or(false);

        let tx_hash = receipt
            .get("receipt")
            .and_then(|r| r.get("transaction_hash"))
            .and_then(Value::as_str)
            .unwrap_or(&self.transaction_hash);

        let block_hash = receipt
            .get("receipt")
            .and_then(|r| r.get("block_hash"))
            .and_then(Value::as_str)
            .unwrap_or("unknown");

        // Try to extract contract-related information
        let contract_address = receipt
            .get("receipt")
            .and_then(|r| r.get("op_result"))
            .and_then(|op| op.get("contract_address"))
            .and_then(Value::as_str);

        let op_type = receipt
            .get("receipt")
            .and_then(|r| r.get("op_result"))
            .and_then(|op| op.get("op_type"))
            .and_then(Value::as_i64);

        if verbosity.is_json() {
            println!("{}", format_output(receipt, true));
        } else {
            println!("\n{}: {}", "Transaction Hash".green().bold(), tx_hash);
            println!("{}: {}", "Block Hash".green().bold(), block_hash);

            let status_str = if status {
                "Success [OK]"
            } else {
                "Failed [ERROR]"
            };
            let status_color = if status {
                status_str.green()
            } else {
                status_str.red()
            };
            println!("{}: {}", "Status".green().bold(), status_color);

            // Show operation type
            if let Some(op_type_val) = op_type {
                let op_type_str = match op_type_val {
                    0 => "Contract Creation",
                    1 => "Contract Call",
                    _ => "Unknown Operation",
                };
                println!("{}: {}", "Operation Type".green().bold(), op_type_str);
            }

            // Show contract address if available
            if let Some(addr) = contract_address {
                println!("{}: {}", "Contract Address".green().bold(), addr);
            }

            // Show logs count
            let logs_count = receipt
                .get("receipt")
                .and_then(|r| r.get("logs"))
                .and_then(Value::as_array)
                .map(|logs| logs.len())
                .unwrap_or(0);

            if logs_count > 0 {
                println!("{}: {} events", "Logs".green().bold(), logs_count);
            }

            if verbose {
                println!("\n{}:", "Full Receipt".green().bold());
                let pretty_receipt = serde_json::to_string_pretty(&receipt)
                    .unwrap_or_else(|_| receipt.to_string());
                println!("{}", pretty_receipt);
            }

            println!("\n{}", "Receipt query successful!".green().bold());
        }

        Ok(())
    }
}
