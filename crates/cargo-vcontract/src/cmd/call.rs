use anyhow::{anyhow, Result};
use clap::{Args, Subcommand};
use colored::Colorize;
use contract_build::VerbosityFlags;

use serde_json::{json, Value};
use std::convert::TryFrom;
use std::path::PathBuf;
use tokio::runtime::Runtime;

use crate::cmd::utils::{
    enhanced_rpc_error_handler, format_error, format_output,
    list_contract_functions_with_context, pretty_format_result, wait_for_confirmation,
};
use crate::services::{
    AuthService, ConfigService, MetadataService, NetworkService, ServiceError, ServiceResult,
    ValidationService,
};

/// Call a smart contract function
#[derive(Debug, Args)]
#[clap(name = "call")]
pub struct CallCommand {
    #[clap(subcommand)]
    subcmd: Option<CallSubcommand>,

    /// Contract address (required for execute)
    #[clap(value_parser)]
    contract_address: Option<String>,

    /// Function name to call (required for execute)
    #[clap(value_parser)]
    function_name: Option<String>,

    /// Node URL (override config file setting - currently configured: use 'cargo vcontract config' to view/change)
    #[clap(long)]
    node: Option<String>,

    /// Function arguments (in JSON format)
    #[clap(long, short = 'a', num_args = 0..)]
    args: Vec<String>,

    /// Fuel value
    #[clap(long, default_value = "1000000")]
    fuel: u64,

    /// Dependent transaction hash (execute call on top of this pending transaction)
    #[clap(long)]
    dep_tx: Option<String>,

    /// Verbosity flags
    #[clap(flatten)]
    verbosity: VerbosityFlags,
}

#[derive(Debug, Subcommand)]
enum CallSubcommand {
    /// List available atomic functions in a contract
    #[clap(name = "ls")]
    List {
        /// Path to contract file (.contract file)
        #[clap(value_parser)]
        contract_file: Option<PathBuf>,

        /// Verbosity flags
        #[clap(flatten)]
        verbosity: VerbosityFlags,
    },
}

impl CallCommand {
    pub fn exec(&self) -> ServiceResult<()> {
        // Create tokio runtime
        let runtime = Runtime::new()
            .map_err(|e| ServiceError::internal(format!("Failed to create tokio runtime: {}", e)))?;
        runtime.block_on(self.async_exec())
    }

    async fn async_exec(&self) -> ServiceResult<()> {
        // Initialize services
        let config_service = ConfigService::new();
        let validation_service = ValidationService::new();
        let metadata_service = MetadataService::new();
        let network_service = NetworkService::new();
        let auth_service = AuthService::new();

        // Parse verbosity settings
        let verbosity = contract_build::Verbosity::try_from(&self.verbosity)
            .map_err(|e| ServiceError::internal(e.to_string()))?;
        let verbose = verbosity.is_verbose();
        let is_json = verbosity.is_json();

        // Handle subcommands
        if let Some(subcmd) = &self.subcmd {
            return match subcmd {
                CallSubcommand::List {
                    contract_file,
                    verbosity: sub_verbosity,
                } => {
                    let sub_verbosity =
                        match contract_build::Verbosity::try_from(sub_verbosity) {
                            Ok(v) => v,
                            Err(e) => {
                                let error_msg = e.to_string();
                                if sub_verbosity.is_json() {
                                    println!("{}", format_error(&error_msg, true));
                                    return Ok(());
                                } else {
                                    return Err(e);
                                }
                            }
                        };
                    let sub_verbose = sub_verbosity.is_verbose();

                    let contract_file = match contract_file {
                        Some(path) => path.clone(),
                        None => {
                            match metadata_service
                                .find_contract_file_for_deploy_verbose(sub_verbose)
                            {
                                Ok(path) => path,
                                Err(e) => {
                                    if sub_verbosity.is_json() {
                                        crate::handle_service_error(&e, true);
                                    } else {
                                        crate::handle_service_error(&e, false);
                                    }
                                }
                            }
                        }
                    };
                    list_contract_functions_with_context(
                        &contract_file,
                        sub_verbose,
                        "call",
                        sub_verbosity.is_json(),
                    )
                }
            };
        }

        // Step 1: Validate required parameters
        let contract_address = self.contract_address.as_ref()
            .ok_or_else(|| ServiceError::validation(
                "Contract address is required. Usage: cargo vcontract call <contract_address> <function_name>".to_string()
            ))?;

        let function_name = self.function_name.as_ref()
            .ok_or_else(|| ServiceError::validation(
                "Function name is required. Usage: cargo vcontract call <contract_address> <function_name>".to_string()
            ))?;

        // Step 2: Validate fuel parameter
        validation_service.validate_fuel(self.fuel)?;

        // Step 3: Get node URL from configuration
        let node_url = config_service
            .get_node_url(self.node.as_deref().unwrap_or("127.0.0.1:9877"))?;

        // Step 4: Connect to network
        let client = network_service.connect_to_node(&node_url).await?;

        // Step 5: Parse and validate arguments
        let args = match metadata_service.find_contract_file_for_deploy_verbose(false) {
            Ok(contract_file) => {
                // Set current contract address for parameter display
                std::env::set_var("VCONTRACT_CURRENT_ADDRESS", &contract_address);

                validation_service.validate_types(
                    &self.args,
                    &contract_file,
                    false,
                    Some(function_name),
                    is_json,
                )?
            }
            Err(_) => {
                // If no contract file found, use basic JSON validation
                validation_service.validate_json_args(&self.args)?
            }
        };

        // Step 6: Get authentication credentials
        let private_key = auth_service.get_saved_private_key()
            .map_err(|e| ServiceError::auth(e.to_string()))?;

        // Step 7: Build and submit transaction
        if verbose && !is_json {
            println!("{}", "Building contract call transaction...".blue());
            println!(
                "{} Using fuel: {}",
                "[FUEL]".yellow(),
                self.fuel.to_string().green()
            );
        }

        let params = json!([{
            "contract_address": contract_address,
            "function_name": function_name,
            "args": args,
            "dependent_transaction_hash": self.dep_tx.as_deref().unwrap_or(""),
            "fuel": self.fuel,
            "privatekey": private_key
        }]);

        let response = client.request("contract.execute", Some(params)).await
            .map_err(|e| ServiceError::network(format!("RPC request failed: {}", e)))?;

        let tx_hash = response.get("transaction_hash")
            .and_then(|v| v.as_str())
            .ok_or_else(|| ServiceError::network(format!(
                "Invalid transaction hash response. Full response: {}",
                response
            )))?;

        // Step 8: Wait for transaction confirmation
        let receipt = wait_for_confirmation(&client, tx_hash, !is_json).await
            .map_err(|e| ServiceError::network(format!("Failed to wait for confirmation: {}", e)))?;

        // Step 9: Process and display results
        let status_bool = receipt
            .get("receipt")
            .and_then(|r| r.get("status"))
            .and_then(Value::as_bool)
            .unwrap_or(false);

        let status_str = if status_bool { "success" } else { "failed" };

        let call_result = receipt
            .get("receipt")
            .and_then(|r| r.get("op_result"))
            .and_then(|op| op.get("return_data"))
            .cloned()
            .unwrap_or(json!(null));

        let fuel_consumed = receipt
            .get("receipt")
            .and_then(|r| r.get("logs"))
            .and_then(|logs| logs.as_array())
            .and_then(|logs| {
                for log in logs {
                    if let Some(log_str) = log.as_str() {
                        if log_str.contains("Fuel consumed:") {
                            if let Some(fuel_part) =
                                log_str.split("Fuel consumed:").nth(1)
                            {
                                if let Ok(fuel_val) = fuel_part.trim().parse::<u64>() {
                                    return Some(fuel_val);
                                }
                            }
                        }
                    }
                }
                None
            })
            .unwrap_or(0);

        let success = status_bool;

        let result = json!({
            "transaction_hash": tx_hash,
            "contract_address": contract_address,
            "function_name": function_name,
            "status": status_str,
            "success": success,
            "result": call_result,
            "receipt": receipt
        });

        if is_json {
            println!("{}", format_output(result, true));
        } else {
            println!("\n{}: {}", "Transaction Hash".green().bold(), tx_hash);
            println!(
                "{}: {}",
                "Contract Address".green().bold(),
                contract_address
            );
            println!("{}: {}", "Function Name".green().bold(), function_name);
            if !args.is_empty() {
                let args_display = if args.len() == 1 {
                    format!("[{}]", args[0])
                } else {
                    format!(
                        "[{}]",
                        args.iter()
                            .map(|v| v.to_string())
                            .collect::<Vec<_>>()
                            .join(", ")
                    )
                };
                println!("{}: {}", "Arguments".green().bold(), args_display);
            }
            println!("{}: {}", "Execution Status".green().bold(), status_str);
            if fuel_consumed > 0 {
                println!(
                    "{} {}: {}",
                    "[GAS]".yellow(),
                    "Fuel Consumed".green().bold(),
                    fuel_consumed.to_string().yellow()
                );
            }

            if success {
                println!(
                    "{}: {}",
                    "Return Value".green().bold(),
                    pretty_format_result(&call_result)
                );

                if verbose {
                    println!("{}:", "Full Receipt".green().bold());
                    let pretty_receipt = serde_json::to_string_pretty(&receipt)
                        .unwrap_or_else(|_| receipt.to_string());
                    println!("{}", pretty_receipt);
                }
            } else {
                let pretty_receipt = serde_json::to_string_pretty(&receipt)
                    .unwrap_or_else(|_| receipt.to_string());

                println!(
                    "{}: {}",
                    "Error Details".red().bold(),
                    format!("Contract call failed. Full receipt:\n{}", pretty_receipt)
                );
            }

            if success {
                println!("\n{}", "Contract call successful!".green().bold());
            } else {
                println!("\n{}", "Contract call failed!".red().bold());
            }
        }

        Ok(())
    }
}
