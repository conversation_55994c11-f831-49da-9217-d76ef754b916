mod cmd;
mod error;
mod services;

use crate::cmd::config::ConfigCommand;
use crate::cmd::{
    BuildCommand, CallCommand, DeployCommand, ForkCommand, KeyCommand, QueryCommand,
    ReceiptCommand, UpgradeCommand,
};
use crate::error::{check_is_json, handle_error};
use crate::services::{ServiceError, ServiceResult};
use anyhow::{anyhow, Error, Result};
use clap::{Args, Parser, Subcommand};
use serde_json::json;

use contract_build::OutputType;
use std::fmt::Debug;
use std::path::PathBuf;

/// VContract is a set of utilities to develop Wasm smart contracts.
#[derive(Debug, Parser)]
#[command(bin_name = "cargo")]
pub(crate) enum Opts {
    /// Utilities to develop Wasm smart contracts.
    #[command(name = "vcontract")]
    VContract(VContractArgs),
}

#[derive(Debug, Args)]
pub(crate) struct VContractArgs {
    #[clap(subcommand)]
    cmd: Command,
}

#[derive(Debug, Subcommand)]
enum Command {
    /// Setup and create a new smart contract project
    #[clap(name = "new")]
    New {
        /// The name of the newly created smart contract
        name: String,
        /// The optional target directory for the contract project
        #[clap(short, long, value_parser)]
        target_dir: Option<PathBuf>,
    },
    /// Compiles the contract, generates metadata, bundles both together in a
    /// `<n>.contract` file
    #[clap(name = "build")]
    Build(BuildCommand),
    /// Calls a function on a deployed smart contract
    #[clap(name = "call")]
    Call(CallCommand),
    /// Deploy a smart contract to the VGraph network
    #[clap(name = "deploy")]
    Deploy(DeployCommand),
    /// Upgrades an existing upgradable smart contract
    #[clap(name = "upgrade")]
    Upgrade(UpgradeCommand),
    /// Query a contract function (read-only, no login required)
    #[clap(name = "query")]
    Query(QueryCommand),
    /// Query transaction receipt by hash
    #[clap(name = "receipt")]
    Receipt(ReceiptCommand),
    /// Fork a transaction to create a new branch
    #[clap(name = "fork")]
    Fork(ForkCommand),
    /// Manage VGraph configuration
    #[clap(name = "config")]
    Config(ConfigCommand),
    /// Manage authentication keys
    #[clap(name = "key")]
    Key(KeyCommand),
    /// Show version information
    #[clap(name = "version")]
    Version,
}

fn main() {
    tracing_subscriber::fmt::init();

    let Opts::VContract(args) = Opts::parse();

    let is_json = check_is_json(&args.cmd);
    match exec(args.cmd) {
        Ok(_) => {}
        Err(err) => {
            handle_error(&err, is_json);
        }
    }
}

fn exec(cmd: Command) -> ServiceResult<()> {
    match &cmd {
        Command::New { name, target_dir } => {
            contract_build::new_contract_project(name, target_dir.as_ref())?;
            println!("Created contract {name}");
            Ok(())
        }
        Command::Build(build) => {
            let result = build.exec()?;

            if matches!(result.output_type, OutputType::Json) {
                println!("{}", result.serialize_json().map_err(|e| ServiceError::internal(e.to_string()))?);
            } else if result.verbosity.is_verbose() {
                println!("{}", result.display());
            }
            Ok(())
        }
        Command::Call(call) => call.exec(),
        Command::Deploy(deploy) => deploy.exec(),
        Command::Upgrade(upgrade) => upgrade.exec(),
        Command::Query(query) => query.exec(),
        Command::Receipt(receipt) => receipt.exec(),
        Command::Fork(fork) => fork.exec(),
        Command::Config(config) => config.exec(),
        Command::Key(key) => key.exec(),
        Command::Version => {
            println!("{} v{}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"));
            println!("Utilities to develop Wasm smart contracts for VGraph network");
            Ok(())
        }
    }
}




