# cargo-vcontract 重构待办事项清单

## 第一阶段：统一错误处理（优先级：高）

- [x] 确保所有命令实现返回 `ServiceResult` 而非 `anyhow::Result`
- [x] 修改 `main.rs` 中的 `exec` 函数使用统一的错误处理机制
- [x] 消除 `format_err` 函数，直接使用 `ServiceError`
- [x] 简化各命令中的错误处理代码，消除重复判断
- [x] 将 `handle_service_error` 函数从主函数移至公共工具模块

## 第二阶段：输出格式化统一（优先级：高）

- [ ] 创建 `output` 模块用于统一处理输出格式
- [ ] 实现 `OutputFormatter` 类，处理不同命令的结果输出
- [ ] 消除各命令中重复的输出格式处理代码
- [ ] 统一 JSON 和人类可读输出的风格
- [ ] 提供彩色输出和普通输出模式切换

## 第三阶段：简化命令实现（优先级：中）

- [ ] 重构 `DeployCommand`，将业务逻辑移至 `DeployService`
- [ ] 重构 `BuildCommand`，简化参数处理和业务逻辑
- [ ] 重构 `CallCommand`，简化和统一实现
- [ ] 重构 `QueryCommand`，减少代码重复
- [ ] 重构 `UpgradeCommand`，改进错误处理
- [ ] 重构 `ForkCommand`，改进输出处理
- [ ] 重构 `ReceiptCommand`，简化实现
- [ ] 重构 `KeyCommand`，改进错误处理
- [ ] 重构 `ConfigCommand`，简化实现

## 第四阶段：配置管理改进（优先级：中）

- [ ] 设计统一的配置管理接口
- [ ] 实现配置文件读取和环境变量覆盖
- [ ] 实现命令行参数覆盖配置
- [ ] 添加配置验证逻辑
- [ ] 简化配置使用方式，减少重复代码

## 第五阶段：重构构建流程（优先级：中）

- [ ] 将构建过程拆分为清晰的步骤和组件
- [ ] 实现 `CargoCompiler` 类，专注于 cargo 编译
- [ ] 实现 `WasmOptimizer` 类，专注于 Wasm 优化
- [ ] 实现 `MetadataGenerator` 类，专注于元数据生成
- [ ] 改进构建过程的错误处理和进度报告

## 第六阶段：测试改进（优先级：低）

- [ ] 为服务层添加单元测试
- [ ] 为命令实现添加集成测试
- [ ] 为构建流程添加单元测试
- [ ] 实现简单的模拟测试框架

## 第七阶段：文档改进（优先级：低）

- [ ] 为服务层接口添加详细文档注释
- [ ] 为命令实现添加文档注释
- [ ] 更新用户文档，反映新架构
- [ ] 为开发者添加架构文档

## 第八阶段：依赖管理（优先级：低）

- [ ] 检查并更新过时的依赖
- [ ] 考虑将 git 依赖替换为发布版本
- [ ] 明确指定依赖版本范围
- [ ] 减少不必要的依赖

## 实施注意事项

1. **保持测试通过**：每个更改后确保测试通过
2. **增量提交**：每个小改动单独提交，便于跟踪和回滚
3. **保持功能完整**：每个阶段结束后应保持所有功能正常工作
4. **考虑向后兼容**：避免破坏现有用户的使用体验
5. **关注核心问题**：优先解决架构和错误处理问题
