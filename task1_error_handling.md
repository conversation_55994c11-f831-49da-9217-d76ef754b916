# 任务1：统一错误处理实施计划

本任务为重构第一阶段的核心任务，目标是统一错误处理机制，提高代码可维护性和错误信息的一致性。

## 详细实施步骤

### 1. 移动错误处理函数到专用模块

- [x] 创建 `src/error.rs` 文件
- [x] 将 `main.rs` 中的 `handle_service_error` 函数移动到 `error.rs`
- [ ] 在 `error.rs` 中添加以下函数:
  - [ ] `pub fn handle_error(err: ServiceError, is_json: bool)` - 处理一般错误
  - [ ] `pub fn format_output_error(message: &str, is_json: bool) -> String` - 格式化错误输出
- [ ] 在 `lib.rs` 或 `mod.rs` 中导出新的错误处理函数
- [x] 更新 `main.rs` 使用新的错误处理模块

### 2. 修改 `exec` 函数返回 `ServiceResult`

- [x] 修改 `main.rs` 中的 `exec` 函数签名为 `fn exec(cmd: Command) -> ServiceResult<()>`
- [x] 删除 `format_err` 函数
- [x] 修改所有 `exec` 返回错误的地方，确保返回 `ServiceError` 类型
- [x] 更新 `main` 函数中的错误处理，使用新的错误处理函数

### 3. 统一 `BuildCommand` 错误处理

- [x] 修改 `BuildCommand::exec` 函数签名为返回 `ServiceResult<BuildResult>`
- [x] 将所有 `anyhow::Error` 转换为 `ServiceError`
- [x] 移除重复的错误处理判断逻辑
- [x] 更新调用 `build.exec()` 的代码以处理 `ServiceResult`

### 4. 统一 `DeployCommand` 错误处理

- [x] 修改 `DeployCommand::exec` 和 `async_exec` 函数签名为返回 `ServiceResult<()>`
- [x] 删除函数中所有直接调用 `handle_service_error` 的代码
- [x] 将所有错误直接通过 `?` 操作符传播
- [x] 将所有服务调用的错误通过 `?` 操作符传播
- [x] 删除重复的错误处理条件分支

### 5. 统一 `CallCommand` 错误处理

- [x] 修改 `CallCommand::exec` 函数签名为返回 `ServiceResult<()>`
- [x] 简化错误处理代码，统一使用 `?` 操作符传播错误
- [x] 删除重复的错误处理条件分支

### 6. 统一 `QueryCommand` 错误处理

- [x] 修改 `QueryCommand::exec` 函数签名为返回 `ServiceResult<()>`
- [x] 简化错误处理代码，统一使用 `?` 操作符传播错误
- [x] 删除重复的错误处理条件分支

### 7. 统一 `UpgradeCommand` 错误处理

- [x] 修改 `UpgradeCommand::exec` 函数签名为返回 `ServiceResult<()>`
- [x] 简化错误处理代码，统一使用 `?` 操作符传播错误
- [x] 删除重复的错误处理条件分支

### 8. 统一 `ForkCommand` 错误处理

- [x] 修改 `ForkCommand::exec` 函数签名为返回 `ServiceResult<()>`
- [x] 简化错误处理代码，统一使用 `?` 操作符传播错误
- [x] 删除重复的错误处理条件分支

### 9. 统一 `ReceiptCommand` 错误处理

- [x] 修改 `ReceiptCommand::exec` 函数签名为返回 `ServiceResult<()>`
- [x] 简化错误处理代码，统一使用 `?` 操作符传播错误
- [x] 删除重复的错误处理条件分支

### 10. 统一 `KeyCommand` 错误处理

- [x] 修改 `KeyCommand::exec` 函数签名为返回 `ServiceResult<()>`
- [x] 简化错误处理代码，统一使用 `?` 操作符传播错误
- [x] 删除重复的错误处理条件分支

### 11. 统一 `ConfigCommand` 错误处理

- [x] 修改 `ConfigCommand::exec` 函数签名为返回 `ServiceResult<()>`
- [x] 简化错误处理代码，统一使用 `?` 操作符传播错误
- [x] 删除重复的错误处理条件分支

### 12. 实现辅助函数简化常见错误处理模式

- [ ] 在 `error.rs` 中添加 `pub fn check_is_json(cmd: &Command) -> bool` 函数
- [ ] 实现 `pub fn convert_anyhow_err(err: anyhow::Error) -> ServiceError` 函数
- [ ] 实现错误上下文添加函数 `pub fn with_context<T>(result: Result<T, ServiceError>, context: &str) -> ServiceResult<T>`

### 13. 测试错误处理

- [ ] 为 `error.rs` 编写单元测试
- [ ] 测试 JSON 模式下的错误输出
- [ ] 测试普通模式下的错误输出
- [ ] 测试错误转换函数

## 验收标准

1. 所有命令实现都返回 `ServiceResult` 类型
2. 主函数中统一处理所有错误
3. 错误处理代码集中在 `error.rs` 模块中
4. 无重复的错误处理逻辑
5. 错误信息格式一致，无论是 JSON 还是普通输出
6. 所有测试通过

## 预计工时

- 移动错误处理函数: 1小时
- 修改主函数和 `exec`: 1小时
- 统一所有命令错误处理: 6小时
- 实现辅助函数: 1小时
- 测试: 2小时

**总计**: 约11小时工作量
